#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试剪切功能修复后的效果
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_clipboard_manager():
    """测试全局剪贴板管理器"""
    print("🧪 测试全局剪贴板管理器修复")
    print("-" * 40)
    
    try:
        from src.utils.global_clipboard_manager import clipboard_manager
        
        # 测试剪切数据存储
        test_data = [["测试数据1"], ["测试数据2"]]
        test_cells = [(0, 0), (1, 0)]
        
        # 模拟剪切操作
        clipboard_manager.cut_data(
            test_data, 
            "测试表格", 
            cut_cells=test_cells, 
            cut_table="test_table"
        )
        
        # 验证get_clipboard_data返回值
        result = clipboard_manager.get_clipboard_data()
        print(f"  ✅ get_clipboard_data返回值数量: {len(result)}")
        
        if len(result) == 5:
            data, source_info, is_cut, cut_cells, cut_table = result
            print(f"  ✅ 数据: {data is not None}")
            print(f"  ✅ 剪切状态: {is_cut}")
            print(f"  ✅ 剪切源单元格: {cut_cells == test_cells}")
            print(f"  ✅ 剪切源表格: {cut_table == 'test_table'}")
        else:
            print(f"  ❌ 返回值数量不正确: 期望5个，实际{len(result)}个")
        
        print("  ✅ 全局剪贴板管理器测试通过")
        
    except Exception as e:
        print(f"  ❌ 全局剪贴板管理器测试失败: {e}")
        import traceback
        print(f"  详细错误: {traceback.format_exc()}")

def test_table_selection_handler():
    """测试表格选择处理器"""
    print("\n🔧 测试表格选择处理器修复")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication, QTableWidget, QTableWidgetItem
        from src.utils.table_selection_handler import TableSelectionHandler
        
        # 创建应用程序实例（如果不存在）
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建测试表格
        table = QTableWidget(3, 3)
        
        # 添加测试数据
        for i in range(3):
            for j in range(3):
                item = QTableWidgetItem(f"测试{i}-{j}")
                table.setItem(i, j, item)
        
        # 创建选择处理器
        handler = TableSelectionHandler(table)
        
        # 测试_extract_cell_content方法
        content = handler._extract_cell_content(0, 0)
        print(f"  ✅ _extract_cell_content方法: {content == '测试0-0'}")
        
        # 测试_get_main_window方法
        main_window = handler._get_main_window()
        print(f"  ✅ _get_main_window方法: {main_window is not None}")
        
        print("  ✅ 表格选择处理器测试通过")
        
    except Exception as e:
        print(f"  ❌ 表格选择处理器测试失败: {e}")
        import traceback
        print(f"  详细错误: {traceback.format_exc()}")

def test_method_existence():
    """测试关键方法是否存在"""
    print("\n📋 测试关键方法存在性")
    print("-" * 40)
    
    try:
        from src.utils.table_selection_handler import TableSelectionHandler
        from PyQt5.QtWidgets import QApplication, QTableWidget
        
        if not QApplication.instance():
            app = QApplication([])
        
        table = QTableWidget(2, 2)
        handler = TableSelectionHandler(table)
        
        # 检查关键方法
        methods_to_check = [
            '_extract_cell_content',
            '_get_main_window', 
            'cut_selected_cells',
            '_copy_cells_for_cut',
            '_apply_cut_visual_feedback',
            '_clear_cut_visual_feedback'
        ]
        
        for method_name in methods_to_check:
            has_method = hasattr(handler, method_name)
            print(f"  {'✅' if has_method else '❌'} {method_name}: {'存在' if has_method else '缺少'}")
        
        print("  ✅ 方法存在性检查完成")
        
    except Exception as e:
        print(f"  ❌ 方法存在性检查失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试剪切功能修复")
    print("=" * 50)
    
    test_clipboard_manager()
    test_table_selection_handler()
    test_method_existence()
    
    print("\n✅ 剪切功能修复测试完成")
