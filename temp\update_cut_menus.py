#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新所有表格组件的右键菜单，添加剪切功能
"""

import os
import re

def update_table_widget_menus():
    """更新所有表格组件的右键菜单"""
    
    # 需要更新的表格组件文件
    table_files = [
        "src/views/status_table_widget.py",
        "src/views/change_table_widget.py", 
        "src/views/fees_table_widget.py",
        "src/views/log_table_widget.py",
        "src/views/scene_table_widget.py"
    ]
    
    for file_path in table_files:
        if os.path.exists(file_path):
            print(f"更新 {file_path}")
            update_single_file(file_path)
        else:
            print(f"文件不存在: {file_path}")

def update_single_file(file_path):
    """更新单个文件的右键菜单"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 在复制、粘贴菜单项中添加剪切
        pattern1 = r'(# 复制、粘贴、删除操作\s*\n\s*copy_action = menu\.addAction\("复制"\)\s*\n\s*)(paste_action = menu\.addAction\("粘贴"\))'
        replacement1 = r'\1cut_action = menu.addAction("剪切")\n        \2'
        content = re.sub(pattern1, replacement1, content)
        
        # 2. 在菜单启用/禁用逻辑中添加剪切
        # 处理不同的启用/禁用模式
        patterns_enable = [
            # 模式1: copy_action.setEnabled(has_selection)
            (r'(copy_action\.setEnabled\(has_selection\))', r'\1\n        cut_action.setEnabled(has_selection)'),
            # 模式2: if not has_selection: copy_action.setEnabled(False)
            (r'(copy_action\.setEnabled\(False\))', r'\1\n            cut_action.setEnabled(False)'),
            # 模式3: else: copy_action.setEnabled(True)
            (r'(copy_action\.setEnabled\(True\))', r'\1\n            cut_action.setEnabled(True)')
        ]
        
        for pattern, replacement in patterns_enable:
            content = re.sub(pattern, replacement, content)
        
        # 3. 在菜单事件处理中添加剪切
        pattern3 = r'(elif action == copy_action:\s*\n\s*self\._copy_selection\(\)\s*\n\s*)(elif action == paste_action:)'
        replacement3 = r'\1elif action == cut_action:\n            self._cut_selection()\n        \2'
        content = re.sub(pattern3, replacement3, content)
        
        # 4. 添加_cut_selection方法
        pattern4 = r'(def _copy_selection\(self\):\s*\n\s*"""复制选中内容"""\s*\n\s*if hasattr\(self, \'table_selection_handler\'\):\s*\n\s*self\.table_selection_handler\.copy_selected_cells\(\)\s*\n\s*else:\s*\n\s*self\.logger\.warning\("表格选择处理器未初始化"\)\s*\n\s*)(def _paste_selection\(self\):)'
        replacement4 = r'''\1
    def _cut_selection(self):
        """剪切选中内容"""
        if hasattr(self, 'table_selection_handler'):
            self.table_selection_handler.cut_selected_cells()
        else:
            self.logger.warning("表格选择处理器未初始化")

    \2'''
        content = re.sub(pattern4, replacement4, content, flags=re.MULTILINE | re.DOTALL)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ {file_path} 更新完成")
        
    except Exception as e:
        print(f"❌ 更新 {file_path} 失败: {e}")

if __name__ == "__main__":
    print("🚀 开始批量更新表格组件右键菜单")
    update_table_widget_menus()
    print("✅ 批量更新完成")
