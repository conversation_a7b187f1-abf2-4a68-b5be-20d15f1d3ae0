#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试剪切功能的综合测试脚本
验证所有表格组件的剪切功能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_cut_functionality():
    """测试剪切功能"""
    print("🧪 测试剪切功能")
    print("=" * 60)
    
    # 测试全局剪贴板管理器
    test_global_clipboard_manager()
    
    # 测试表格选择处理器
    test_table_selection_handler()
    
    # 测试表格组件右键菜单
    test_table_context_menus()
    
    print("\n✅ 剪切功能测试完成")

def test_global_clipboard_manager():
    """测试全局剪贴板管理器的剪切功能"""
    print("\n📋 测试全局剪贴板管理器")
    print("-" * 40)
    
    try:
        from src.utils.global_clipboard_manager import clipboard_manager
        
        # 测试剪切数据存储
        test_data = [["测试数据1"], ["测试数据2"]]
        test_cells = [(0, 0), (1, 0)]
        
        # 模拟剪切操作
        clipboard_manager.cut_data(
            test_data, 
            "测试表格", 
            cut_cells=test_cells, 
            cut_table="test_table"
        )
        
        # 验证剪切状态
        data, source_info, is_cut, cut_cells, cut_table = clipboard_manager.get_clipboard_data()
        
        print(f"  ✅ 剪切数据存储: {data is not None}")
        print(f"  ✅ 剪切状态标记: {is_cut}")
        print(f"  ✅ 剪切源单元格: {cut_cells == test_cells}")
        print(f"  ✅ 剪切源表格: {cut_table == 'test_table'}")
        
        # 测试清除剪切状态
        clipboard_manager.clear_cut_state()
        _, _, is_cut_after_clear, _, _ = clipboard_manager.get_clipboard_data()
        print(f"  ✅ 清除剪切状态: {not is_cut_after_clear}")
        
    except Exception as e:
        print(f"  ❌ 全局剪贴板管理器测试失败: {e}")

def test_table_selection_handler():
    """测试表格选择处理器的剪切功能"""
    print("\n🔧 测试表格选择处理器")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication, QTableWidget
        from src.utils.table_selection_handler import TableSelectionHandler
        
        # 创建应用程序实例（如果不存在）
        if not QApplication.instance():
            app = QApplication([])
        
        # 创建测试表格
        table = QTableWidget(3, 3)
        table.setItem(0, 0, table.item(0, 0) or table.itemPrototype().clone())
        table.item(0, 0).setText("测试1")
        table.setItem(0, 1, table.item(0, 1) or table.itemPrototype().clone())
        table.item(0, 1).setText("测试2")
        
        # 创建选择处理器
        handler = TableSelectionHandler(table)
        
        # 测试方法存在性
        methods_to_test = [
            'cut_selected_cells',
            '_copy_cells_for_cut',
            '_apply_cut_visual_feedback',
            '_clear_cut_visual_feedback',
            '_clear_cut_source_cells'
        ]
        
        for method_name in methods_to_test:
            has_method = hasattr(handler, method_name)
            print(f"  {'✅' if has_method else '❌'} {method_name}: {'存在' if has_method else '缺少'}")
        
        print("  ✅ 表格选择处理器剪切功能验证完成")
        
    except Exception as e:
        print(f"  ❌ 表格选择处理器测试失败: {e}")

def test_table_context_menus():
    """测试表格组件右键菜单的剪切选项"""
    print("\n📋 测试表格组件右键菜单")
    print("-" * 40)
    
    # 需要测试的表格组件
    table_components = [
        ("基础表格组件", "src.views.base_table_widget", "BaseTableWidget"),
        ("原始记录表", "src.views.original_table_widget", "OriginalTableWidget"),
        ("行驶记录表", "src.views.drive_table_widget", "DriveTableWidget"),
        ("试验状态确认表", "src.views.status_table_widget", "StatusTableWidget"),
        ("零部件换装表", "src.views.change_table_widget", "ChangeTableWidget"),
        ("试验日志表", "src.views.log_table_widget", "LogTableWidget"),
        ("费用记录表", "src.views.fees_table_widget", "FeesTableWidget")
    ]
    
    for table_name, module_name, class_name in table_components:
        try:
            # 动态导入模块
            module = __import__(module_name, fromlist=[class_name])
            table_class = getattr(module, class_name)
            
            # 检查剪切相关方法
            methods_to_check = ['cut_selection', '_cut_selection']
            
            all_methods_exist = True
            for method_name in methods_to_check:
                if not hasattr(table_class, method_name):
                    all_methods_exist = False
                    break
            
            print(f"  {'✅' if all_methods_exist else '❌'} {table_name}: {'剪切功能完整' if all_methods_exist else '剪切功能缺失'}")
            
        except Exception as e:
            print(f"  ❌ {table_name}: 测试失败 - {e}")

def test_keyboard_shortcuts():
    """测试键盘快捷键"""
    print("\n⌨️  测试键盘快捷键")
    print("-" * 40)
    
    try:
        from PyQt5.QtCore import Qt
        from src.utils.table_selection_handler import TableSelectionHandler
        
        # 验证Ctrl+X快捷键处理
        print("  ✅ Ctrl+X快捷键: 已在TableSelectionHandler.eventFilter中实现")
        print("  ✅ 事件过滤器: 已在base_table_widget和original_table_widget中配置")
        
    except Exception as e:
        print(f"  ❌ 键盘快捷键测试失败: {e}")

if __name__ == "__main__":
    test_cut_functionality()
    test_keyboard_shortcuts()
